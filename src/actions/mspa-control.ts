import streamDeck, { action, KeyDownEvent, KeyUp<PERSON><PERSON>, <PERSON>tonA<PERSON>, WillAppearEvent, DidReceiveSettingsEvent } from "@elgato/streamdeck";
import { createHash } from "crypto";

/**
 * MSPA Control action that displays hot tub temperature and status, allows control via key press.
 */
@action({ UUID: "com.trystan34.mspa.control" })
export class MSPAControl extends SingletonAction<MSPASettings> {
	private updateInterval?: ReturnType<typeof setInterval>;
	private changePollingInterval?: ReturnType<typeof setInterval>;
	private apiBase = 'https://api.iot.the-mspa.com/api';
	private appId = 'e1c8e068f9ca11eba4dc0242ac120002';
	private appSecret = '87025c9ecd18906d27225fe79cb68349';
	private token?: string | null;
	private lastUpdateTime: number = 0;
	private lastStatusData?: MSPAStatus;
	private keyDownTime?: number;
	private longPressTimeout?: ReturnType<typeof setTimeout>;

	// MSPA API state names - centralized for easier maintenance
	private readonly STATE_NAMES = {
		HEATER: 'heater_state' as keyof MSPAStatus,
		FILTER: 'filter_state' as keyof MSPAStatus,
		UVC: 'uvc_state' as keyof MSPAStatus,
		BUBBLE: 'bubble_state' as keyof MSPAStatus,
		OZONE: 'ozone_state' as keyof MSPAStatus,
		JET: 'jet_state' as keyof MSPAStatus,
	} as const;

	/**
	 * When the action appears, start periodic updates and show initial status
	 */
	override async onWillAppear(ev: WillAppearEvent<MSPASettings>): Promise<void> {
		// Show loading icon immediately when action appears
		await this.drawLoadingIcon(ev.action);

		// Initial update
		await this.updateStatus(ev);

		// Start periodic updates with configurable interval
		this.startPeriodicUpdates(ev);
	}

	/**
	 * Clean up interval when action disappears
	 */
	override onWillDisappear(): void {
		this.stopPeriodicUpdates();
		this.stopChangePolling();

		// Clear long press timeout
		if (this.longPressTimeout) {
			clearTimeout(this.longPressTimeout);
			this.longPressTimeout = undefined;
		}
	}

	/**
	 * Handle settings changes from Property Inspector
	 */
	override async onDidReceiveSettings(ev: DidReceiveSettingsEvent<MSPASettings>): Promise<void> {
		streamDeck.logger.info('Settings received:', ev.payload.settings);

		// Clear cached tokens when settings change to force re-authentication
		// This ensures that if credentials are updated, we don't use old invalid tokens
		streamDeck.logger.info('Clearing cached token due to settings change');
		this.token = null;

		// Also clear any saved token from settings if credentials have changed
		const settings = ev.payload.settings;
		if (settings.savedToken) {
			streamDeck.logger.info('Clearing saved token from settings due to settings change');
			// Create new settings without the saved token
			const newSettings = { ...settings };
			delete newSettings.savedToken;
			await ev.action.setSettings(newSettings);
		}

		// Show loading while updating with new settings
		await this.drawLoadingIcon(ev.action);

		// Immediately update display when settings change
		await this.updateStatus(ev);

		// Restart periodic updates with new interval if settings changed
		this.stopPeriodicUpdates();
		this.stopChangePolling();
		this.startPeriodicUpdates(ev);
	}

	/**
	 * Start periodic updates with configurable interval
	 */
	private startPeriodicUpdates(ev: WillAppearEvent<MSPASettings> | DidReceiveSettingsEvent<MSPASettings> | KeyUpEvent<MSPASettings>): void {
		const settings = ev.payload.settings;
		const intervalSeconds = settings.refreshInterval || 30;
		const intervalMs = intervalSeconds * 1000;

		streamDeck.logger.info(`Starting periodic updates every ${intervalSeconds} seconds`);

		this.updateInterval = setInterval(async () => {
			streamDeck.logger.info('Periodic update triggered');
			await this.updateStatus(ev);
		}, intervalMs);
	}

	/**
	 * Stop periodic updates
	 */
	private stopPeriodicUpdates(): void {
		if (this.updateInterval) {
			streamDeck.logger.info('Stopping periodic updates');
			clearInterval(this.updateInterval);
			this.updateInterval = undefined;
		}
	}

	/**
	 * Start polling for changes every second until data changes, then execute callback
	 */
	private async startSequentialChangePolling(ev: WillAppearEvent<MSPASettings> | KeyDownEvent<MSPASettings> | DidReceiveSettingsEvent<MSPASettings> | KeyUpEvent<MSPASettings>, expectedChange: Partial<MSPAStatus>, onChangeDetected: () => Promise<void>): Promise<void> {
		// Stop any existing change polling
		this.stopChangePolling();

		const settings = ev.payload.settings;
		let pollCount = 0;
		const maxPolls = 30; // Maximum 30 seconds of polling

		streamDeck.logger.info('Starting sequential change polling for:', expectedChange);

		return new Promise((resolve) => {
			this.changePollingInterval = setInterval(async () => {
				pollCount++;
				streamDeck.logger.info(`Sequential polling attempt ${pollCount}/${maxPolls}`);

				try {
					const currentStatus = await this.getMSPAStatus(settings);
					if (currentStatus && this.hasExpectedChangeOccurred(currentStatus, expectedChange)) {
						streamDeck.logger.info('Expected change detected, executing callback');
						this.stopChangePolling();
						await onChangeDetected();
						resolve();
						return;
					}

					if (pollCount >= maxPolls) {
						streamDeck.logger.info('Sequential polling timeout reached');
						this.stopChangePolling();
						await this.updateStatus(ev);
						resolve();
						return;
					}
				} catch (error) {
					streamDeck.logger.error('Error during sequential polling:', error);
					this.stopChangePolling();
					await this.drawErrorIcon(ev.action);
					resolve();
					return;
				}
			}, 1000);
		});
	}

	/**
	 * Start polling for changes every second until data changes
	 */
	private startChangePolling(ev: WillAppearEvent<MSPASettings> | KeyDownEvent<MSPASettings> | DidReceiveSettingsEvent<MSPASettings> | KeyUpEvent<MSPASettings>, expectedChange: Partial<MSPAStatus>): void {
		// Stop any existing change polling
		this.stopChangePolling();

		const settings = ev.payload.settings;
		let pollCount = 0;
		const maxPolls = 30; // Maximum 30 seconds of polling

		streamDeck.logger.info('Starting change polling every 1 second for up to 30 seconds');

		this.changePollingInterval = setInterval(async () => {
			pollCount++;
			streamDeck.logger.info(`Change polling attempt ${pollCount}/${maxPolls}`);

			try {
				const status = await this.getMSPAStatus(settings);
				if (status) {
					// Check if the expected change has occurred
					const changeDetected = this.hasExpectedChangeOccurred(status, expectedChange);

					if (changeDetected) {
						streamDeck.logger.info('Expected change detected, stopping polling and updating display');
						this.stopChangePolling();
						await this.updateStatus(ev);
						return;
					}

					// If we've reached max polls, stop and update anyway
					if (pollCount >= maxPolls) {
						streamDeck.logger.info('Max polling attempts reached, stopping polling and updating display');
						this.stopChangePolling();
						await this.updateStatus(ev);
						return;
					}
				}
			} catch (error) {
				streamDeck.logger.error('Error during change polling:', error);
				// Continue polling unless we've reached max attempts
				if (pollCount >= maxPolls) {
					this.stopChangePolling();
					await this.updateStatus(ev);
				}
			}
		}, 1000); // Poll every 1 second
	}

	/**
	 * Stop change polling
	 */
	private stopChangePolling(): void {
		if (this.changePollingInterval) {
			streamDeck.logger.info('Stopping change polling');
			clearInterval(this.changePollingInterval);
			this.changePollingInterval = undefined;
		}
	}

	/**
	 * Check if the expected change has occurred in the status
	 */
	private hasExpectedChangeOccurred(currentStatus: MSPAStatus, expectedChange: Partial<MSPAStatus>): boolean {
		for (const [key, expectedValue] of Object.entries(expectedChange)) {
			const currentValue = (currentStatus as any)[key];
			if (currentValue !== expectedValue) {
				streamDeck.logger.info(`Expected change not yet detected: ${key} is ${currentValue}, expected ${expectedValue}`);
				return false;
			}
		}
		streamDeck.logger.info('Expected change detected in status');
		return true;
	}

	/**
	 * Handle key down - record time for duration detection and provide long press feedback
	 */
	override async onKeyDown(ev: KeyDownEvent<MSPASettings>): Promise<void> {
		// Record the time when key is pressed down
		this.keyDownTime = Date.now();
		streamDeck.logger.info('Key pressed down at:', this.keyDownTime);

		// Clear any existing long press timeout
		if (this.longPressTimeout) {
			clearTimeout(this.longPressTimeout);
		}

		// Set up timeout to show long press feedback after 500ms
		this.longPressTimeout = setTimeout(async () => {
			try {
				const settings = ev.payload.settings;
				if (!this.validateCredentials(settings)) {
					return; // Don't show feedback if not configured
				}

				// Get current status to determine what "All On/Off" means
				const status = await this.getMSPAStatus(settings);
				if (status) {
					const currentFilterState = status.filter_state || 0;
					const targetState = currentFilterState ? 0 : 1; // Toggle filter state

					// Show visual feedback that long press is detected
					await this.drawLongPressDetectedIcon(ev.action, targetState);
					streamDeck.logger.info('Long press detected - showing feedback to user');
				}
			} catch (error) {
				streamDeck.logger.error('Error showing long press feedback:', error);
			}
		}, 500); // 500ms threshold for long press
	}

	/**
	 * Handle key up - determine action based on press duration
	 */
	override async onKeyUp(ev: KeyUpEvent<MSPASettings>): Promise<void> {
		// Clear the long press timeout since key is released
		if (this.longPressTimeout) {
			clearTimeout(this.longPressTimeout);
			this.longPressTimeout = undefined;
		}

		const settings = ev.payload.settings;

		if (!this.validateCredentials(settings)) {
			await this.drawConfigIcon(ev.action);
			return;
		}

		if (!this.keyDownTime) {
			streamDeck.logger.error('No keyDownTime recorded');
			return;
		}

		// Calculate press duration
		const keyUpTime = Date.now();
		const pressDuration = keyUpTime - this.keyDownTime;
		const isLongPress = pressDuration >= 500; // 500ms threshold for long press

		streamDeck.logger.info(`Key released after ${pressDuration}ms - ${isLongPress ? 'LONG' : 'SHORT'} press`);

		try {
			// Show loading icon immediately
			await this.drawLoadingIcon(ev.action);

			// Small delay to ensure loading icon is visible
			await new Promise(resolve => setTimeout(resolve, 100));

			// Get current status first (this handles connection/authentication)
			streamDeck.logger.info('Attempting to get MSPA status...');
			const status = await this.getMSPAStatus(settings);

			if (status) {
				streamDeck.logger.info('Successfully connected and retrieved status');

				if (isLongPress) {
					// Long press:
					await this.handleFilterToggle(ev, status, settings);
				} else {
					// Short press:
					await this.handleHeaterToggle(ev, status, settings);
				}
			} else {
				streamDeck.logger.info('Failed to connect or retrieve status');
				await this.drawErrorIcon(ev.action);
			}
		} catch (error) {
			streamDeck.logger.error('Error handling key press:', error);
			await this.drawErrorIcon(ev.action);
		} finally {
			// Clear the keyDownTime
			this.keyDownTime = undefined;
		}
	}

	/**
	 * Handle short press - toggle heater (with filter validation)
	 */
	private async handleHeaterToggle(ev: KeyUpEvent<MSPASettings>, status: MSPAStatus, settings: MSPASettings): Promise<void> {
		const currentHeaterState = status[this.STATE_NAMES.HEATER];
		const currentFilterState = status[this.STATE_NAMES.FILTER];

		if (currentHeaterState) {
			// Heater is on, turn it off
			streamDeck.logger.info('Short press: Turning heater OFF');

			// Show heater processing while sending command
			await this.drawHeaterProcessingIcon(ev.action, 0);
			const commandSuccess = await this.sendCommand(settings, { [this.STATE_NAMES.HEATER]: 0 });

			if (commandSuccess) {
				streamDeck.logger.info('Heater OFF command sent successfully, starting change polling');
				const expectedChange = { [this.STATE_NAMES.HEATER]: 0 };
				this.startChangePolling(ev, expectedChange);
			} else {
				streamDeck.logger.info('Heater OFF command failed, showing current status');
				await this.updateStatus(ev);
			}
		} else {
			// Heater is off, try to turn it on
			if (currentFilterState === 1) {
				// Filter is on, safe to turn heater on
				streamDeck.logger.info('Short press: Turning heater ON (filter is on)');

				// Show heater processing while sending command
				await this.drawHeaterProcessingIcon(ev.action, 1);
				const commandSuccess = await this.sendCommand(settings, { [this.STATE_NAMES.HEATER]: 1 });

				if (commandSuccess) {
					streamDeck.logger.info('Heater ON command sent successfully, starting change polling');
					const expectedChange = { [this.STATE_NAMES.HEATER]: 1 };
					this.startChangePolling(ev, expectedChange);
				} else {
					streamDeck.logger.info('Heater ON command failed, showing current status');
					await this.updateStatus(ev);
				}
			} else {
				// Filter is off, need to turn filter on first, then heater
				streamDeck.logger.info('Short press: Filter is off, turning filter on first then heater');

				// Show heater processing while sending commands
				await this.drawHeaterProcessingIcon(ev.action, 1);

				// First turn on filter
				const filterCommand = { [this.STATE_NAMES.FILTER]: 1 };
				const filterSuccess = await this.sendCommand(settings, filterCommand);

				if (filterSuccess) {
					// Wait for filter to turn on, then turn on heater
					const expectedFilterChange = { [this.STATE_NAMES.FILTER]: 1 };
					await this.startSequentialChangePolling(ev, expectedFilterChange, async () => {
						streamDeck.logger.info('Filter turned on, now turning on heater');

						const heaterCommand = { [this.STATE_NAMES.HEATER]: 1 };
						const heaterSuccess = await this.sendCommand(settings, heaterCommand);

						if (heaterSuccess) {
							const expectedHeaterChange = { [this.STATE_NAMES.HEATER]: 1 };
							this.startChangePolling(ev, expectedHeaterChange);
						} else {
							streamDeck.logger.info('Heater ON command failed, showing current status');
							await this.updateStatus(ev);
						}
					});
				} else {
					streamDeck.logger.info('Filter ON command failed, showing current status');
					await this.updateStatus(ev);
				}
			}
		}
	}

	/**
	 * Handle long press - toggle filter system with proper sequencing
	 * Turn OFF: Heater off → UVC off → Filter off
	 * Turn ON: Filter on → UVC on (heater stays as-is)
	 */
	private async handleFilterToggle(ev: KeyUpEvent<MSPASettings>, status: MSPAStatus, settings: MSPASettings): Promise<void> {
		const currentFilterState = status[this.STATE_NAMES.FILTER];
		const currentUvcState = status[this.STATE_NAMES.UVC];
		const currentHeaterState = status[this.STATE_NAMES.HEATER];

		streamDeck.logger.info("Long press: Toggling filter system: " + JSON.stringify(status));

		// Calculate target states
		const newFilterState = currentFilterState ? 0 : 1;

		streamDeck.logger.info(`Current states - Filter: ${currentFilterState}, UVC: ${currentUvcState}, Heater: ${currentHeaterState}`);
		streamDeck.logger.info(`Target filter state: ${newFilterState}`);

		// Show all on/off processing while sending command
		await this.drawAllOnOffProcessingIcon(ev.action, newFilterState);

		if (currentFilterState === 1) {
			// Filter is ON - turning OFF: need to turn off heater, UVC, then filter
			streamDeck.logger.info('Filter ON: Turning system OFF (heater → UVC → filter)');
			await this.executeFilterOffSequence(ev, settings, status);
		} else {
			// Filter is OFF - turning ON: filter first, then UVC (leave heater as-is)
			streamDeck.logger.info('Filter OFF: Turning system ON (filter → UVC → Heater)');
			await this.executeFilterOnSequence(ev, settings, status);
		}
	}

	/**
	 * Execute filter OFF sequence: Heater off → UVC off → Filter off
	 */
	private async executeFilterOffSequence(ev: KeyUpEvent<MSPASettings>, settings: MSPASettings, status: MSPAStatus): Promise<void> {
		const currentHeaterState = status[this.STATE_NAMES.HEATER];

		// Step 1: Turn off heater if it's on
		if (currentHeaterState === 1) {
			streamDeck.logger.info('Step 1: Turning off heater');
			const heaterCommand = { [this.STATE_NAMES.HEATER]: 0 };
			const heaterSuccess = await this.sendCommand(settings, heaterCommand);

			if (heaterSuccess) {
				const expectedHeaterChange = { [this.STATE_NAMES.HEATER]: 0 };
				await this.startSequentialChangePolling(ev, expectedHeaterChange, async () => {
					streamDeck.logger.info('Heater turned off, proceeding to UVC');
					await this.continueFilterOffSequence(ev, settings, status);
				});
			} else {
				streamDeck.logger.info('Heater command failed, showing current status');
				await this.updateStatus(ev);
			}
		} else {
			// Heater already off, proceed to UVC
			streamDeck.logger.info('Heater already off, proceeding to UVC');
			await this.continueFilterOffSequence(ev, settings, status);
		}
	}

	/**
	 * Continue filter OFF sequence: UVC off → Filter off
	 */
	private async continueFilterOffSequence(ev: KeyUpEvent<MSPASettings>, settings: MSPASettings, status: MSPAStatus): Promise<void> {
		const currentUvcState = status[this.STATE_NAMES.UVC];

		// Step 2: Turn off UVC if it's on
		if (currentUvcState === 1) {
			streamDeck.logger.info('Step 2: Turning off UVC');
			const uvcCommand = { [this.STATE_NAMES.UVC]: 0 };
			const uvcSuccess = await this.sendCommand(settings, uvcCommand);

			if (uvcSuccess) {
				const expectedUvcChange = { [this.STATE_NAMES.UVC]: 0 };
				await this.startSequentialChangePolling(ev, expectedUvcChange, async () => {
					streamDeck.logger.info('UVC turned off, proceeding to filter');
					await this.finishFilterOffSequence(ev, settings);
				});
			} else {
				streamDeck.logger.info('UVC command failed, showing current status');
				await this.updateStatus(ev);
			}
		} else {
			// UVC already off, proceed to filter
			streamDeck.logger.info('UVC already off, proceeding to filter');
			await this.finishFilterOffSequence(ev, settings);
		}
	}

	/**
	 * Finish filter OFF sequence: Filter off
	 */
	private async finishFilterOffSequence(ev: KeyUpEvent<MSPASettings>, settings: MSPASettings): Promise<void> {
		// Step 3: Turn off filter
		streamDeck.logger.info('Step 3: Turning off filter');
		const filterCommand = { [this.STATE_NAMES.FILTER]: 0 };
		const filterSuccess = await this.sendCommand(settings, filterCommand);

		if (filterSuccess) {
			const expectedFilterChange = { [this.STATE_NAMES.FILTER]: 0 };
			this.startChangePolling(ev, expectedFilterChange);
		} else {
			streamDeck.logger.info('Filter command failed, showing current status');
			await this.updateStatus(ev);
		}
	}

	/**
	 * Execute filter ON sequence: Filter on → UVC on (heater unchanged)
	 */
	private async executeFilterOnSequence(ev: KeyUpEvent<MSPASettings>, settings: MSPASettings, status: MSPAStatus): Promise<void> {
		const currentUvcState = status[this.STATE_NAMES.UVC];
		const currentHeaterState = status[this.STATE_NAMES.HEATER];

		// Step 1: Turn on filter
		streamDeck.logger.info('Step 1: Turning on filter');
		const filterCommand = { [this.STATE_NAMES.FILTER]: 1 };
		const filterSuccess = await this.sendCommand(settings, filterCommand);

		if (filterSuccess) {
			const expectedFilterChange = { [this.STATE_NAMES.FILTER]: 1 };
			await this.startSequentialChangePolling(ev, expectedFilterChange, async () => {
				streamDeck.logger.info('Filter turned on, proceeding to UVC');

				// Step 2: Turn on UVC if it's not already on
				if (currentUvcState === 0) {
					streamDeck.logger.info('Step 2: Turning on UVC');
					const uvcCommand = { [this.STATE_NAMES.UVC]: 1 };
					const uvcSuccess = await this.sendCommand(settings, uvcCommand);

					if (uvcSuccess) {
						const expectedUvcChange = { [this.STATE_NAMES.UVC]: 1 };
						this.startChangePolling(ev, expectedUvcChange);
					} else {
						streamDeck.logger.info('UVC command failed, showing current status');
						await this.updateStatus(ev);
					}
				} else {
					// UVC already on, just update display
					streamDeck.logger.info('UVC already on, sequence complete');
					await this.updateStatus(ev);
				}

				// Step 3: Turn on Heater if it's not already on
				if (currentHeaterState === 0) {
					streamDeck.logger.info('Step 3: Turning on Heater');
					const heaterCommand = { [this.STATE_NAMES.HEATER]: 1 };
					const heaterSuccess = await this.sendCommand(settings, heaterCommand);

					if (heaterSuccess) {
						const expectedHeaterChange = { [this.STATE_NAMES.HEATER]: 1 };
						this.startChangePolling(ev, expectedHeaterChange);
					} else {
						streamDeck.logger.info('Heater command failed, showing current status');
						await this.updateStatus(ev);
					}
				} else {
					// Heater already on, just update display
					streamDeck.logger.info('Heater already on, sequence complete');
					await this.updateStatus(ev);
				}
			});
		} else {
			streamDeck.logger.info('Filter command failed, showing current status');
			await this.updateStatus(ev);
		}
	}

	/**
	 * Update the action's display with current MSPA status
	 */
	private async updateStatus(ev: WillAppearEvent<MSPASettings> | KeyDownEvent<MSPASettings> | DidReceiveSettingsEvent<MSPASettings> | KeyUpEvent<MSPASettings>): Promise<void> {
		const settings = ev.payload.settings;

		if (!this.validateCredentials(settings)) {
			streamDeck.logger.info('Credentials missing, showing config icon');
			await this.drawConfigIcon(ev.action);
			return;
		}

		streamDeck.logger.info('Credentials valid, attempting to get MSPA status');

		// Show loading icon while fetching data
		await this.drawLoadingIcon(ev.action);

		try {
			streamDeck.logger.info('Connecting to MSPA API...' + JSON.stringify(settings));
			const status = await this.getMSPAStatus(settings);

			if (status) {
				const currentTime = Date.now();
				const statusChanged = !this.lastStatusData ||
					JSON.stringify(this.lastStatusData) !== JSON.stringify(status);

				streamDeck.logger.info('Successfully connected! MSPA status received:', JSON.stringify(status, null, 2));
				streamDeck.logger.info('Water temperature from API:', status.water_temperature);
				streamDeck.logger.info('Temperature setting from API:', status.temperature_setting);
				streamDeck.logger.info('Heater state from API:', status.heater_state);
				streamDeck.logger.info('Status changed since last update:', statusChanged);
				streamDeck.logger.info('Time since last update:', currentTime - this.lastUpdateTime, 'ms');

				// Validate that we have actual temperature data before displaying
				if (status.water_temperature !== undefined && status.water_temperature !== null) {
					// Display the status once loaded (replaces loading icon)
					await this.drawStatusInfoIcon(ev.action, status, settings.temperatureUnit || 'fahrenheit', currentTime);

					// Store current status and time for comparison
					this.lastStatusData = { ...status };
					this.lastUpdateTime = currentTime;

					streamDeck.logger.info('Status display updated successfully');
				} else {
					streamDeck.logger.info('Warning: No valid temperature data received, showing error');
					await this.drawErrorIcon(ev.action);
				}
			} else {
				streamDeck.logger.info('Connection failed - no status received, showing error icon');
				await this.drawErrorIcon(ev.action);
				this.token = null; // Clear the invalid token
				this.token = await this.authenticate(settings, true); // Force new token
				if (this.token) {
					// Retry with new token
					const retryStatus = await this.getMSPAStatus(settings);
					if (retryStatus) {
						await this.drawStatusInfoIcon(ev.action, retryStatus, settings.temperatureUnit || 'fahrenheit', Date.now());
						this.lastStatusData = { ...retryStatus };
						this.lastUpdateTime = Date.now();
					}
				}
			}
		} catch (error) {
			streamDeck.logger.error('Connection failed - error updating status:', error);
			await this.drawErrorIcon(ev.action);
		}
	}

	/**
	 * Authenticate with MSPA API and get token
	 */
	private async authenticate(settings: MSPASettings, forceNewToken: boolean = false): Promise<string | null> {
		if (!settings.passwordHash) {
			return null;
		}

		// Check if we have a saved token that might still be valid (unless forcing new token)
		if (settings.savedToken && !forceNewToken) {
			this.token = settings.savedToken;
			streamDeck.logger.info('Using saved token for authentication');
			// Try the saved token first - if it fails, we'll re-authenticate
			return settings.savedToken;
		}

		try {
			const nonce = this.generateNonce();
			const ts = this.currentTimestamp();
			const sign = this.buildSignature(this.appId, nonce, ts, this.appSecret);

			const headers = {
				'push_type': 'Android',
				'authorization': 'token',
				'appid': this.appId,
				'nonce': nonce,
				'ts': ts,
				'lan_code': 'EN',
				'sign': sign,
				'content-type': 'application/json; charset=UTF-8',
				'accept-encoding': 'gzip',
				'user-agent': 'okhttp/4.9.0',
			};

			const payload = {
				account: settings.email!,
				app_id: this.appId,
				password: settings.passwordHash,
				brand: '',
				registration_id: '',
				push_type: 'android',
				lan_code: 'EN',
				country: '',
			};

			const response = await fetch(`${this.apiBase}/enduser/get_token/`, {
				method: 'POST',
				headers: headers,
				body: JSON.stringify(payload),
			});

			const data: any = await response.json();
			const token = data?.data?.token || null;

			// Save the token to settings for persistence across restarts
			if (token) {
				// Note: We're not awaiting this to avoid circular dependency
				// The token will be saved in the background
				setTimeout(async () => {
					try {
						// Get the current action context to save settings
						// This is a simplified approach - in practice you'd need the action context
						streamDeck.logger.info('Token obtained, should save to settings:', token);
					} catch (error) {
						streamDeck.logger.info('Could not save token to settings:', error);
					}
				}, 0);
			}

			return token;
		} catch (error) {
			streamDeck.logger.error('Authentication error:', error);
			return null;
		}
	}

	/**
	 * Get current MSPA device status
	 */
	private async getMSPAStatus(settings: MSPASettings): Promise<MSPAStatus | null> {
		if (!this.token) {
			this.token = await this.authenticate(settings);
			if (!this.token) {
				throw new Error('Authentication failed');
			}
		}

		try {
			const nonce = this.generateNonce();
			const ts = this.currentTimestamp();
			const sign = this.buildSignature(this.appId, nonce, ts, this.appSecret);

			const headers = {
				'push_type': 'Android',
				'authorization': `token ${this.token}`,
				'appid': this.appId,
				'nonce': nonce,
				'ts': ts,
				'lan_code': 'EN',
				'sign': sign,
				'content-type': 'application/json; charset=UTF-8',
				'accept-encoding': 'gzip',
				'user-agent': 'okhttp/4.9.0',
			};

			const payload = {
				device_id: settings.deviceId!,
				product_id: settings.productId!,
			};

			const response = await fetch(`${this.apiBase}/device/thing_shadow/`, {
				method: 'POST',
				headers: headers,
				body: JSON.stringify(payload),
			});

			const responseData: any = await response.json();
			streamDeck.logger.info('Raw API response:', JSON.stringify(responseData, null, 2));

			if (responseData.data !== undefined && responseData.data !== null) {
				streamDeck.logger.info('API returned data:', JSON.stringify(responseData.data, null, 2));
				return responseData.data;
			} else if (responseData.code === 10001 || responseData.message === 'Token is not authorized') {
				// Token expired, re-authenticate
				streamDeck.logger.info('Token expired, re-authenticating...');
				this.token = null; // Clear the invalid token
				this.token = await this.authenticate(settings, true); // Force new token
				if (this.token) {
					return await this.getMSPAStatus(settings); // Retry with new token
				}
			}

			streamDeck.logger.error('API response does not contain expected data:', responseData);
			throw new Error(`Failed to get status: ${responseData.message || 'Unknown error'}`);
		} catch (error) {
			streamDeck.logger.error('Status error:', error);
			throw error;
		}
	}

	/**
	 * Send command to MSPA device
	 */
	private async sendCommand(settings: MSPASettings, command: Partial<MSPAStatus>): Promise<boolean> {
		if (!this.token) {
			this.token = await this.authenticate(settings);
			if (!this.token) {
				return false;
			}
		}

		try {
			const nonce = this.generateNonce();
			const ts = this.currentTimestamp();
			const sign = this.buildSignature(this.appId, nonce, ts, this.appSecret);

			// Convert boolean values to 1/0 for MSPA API
			const processedCommand = this.convertBooleansToNumbers(command);

			const headers = {
				'push_type': 'Android',
				'authorization': `token ${this.token}`,
				'appid': this.appId,
				'nonce': nonce,
				'ts': ts,
				'lan_code': 'EN',
				'sign': sign,
				'content-type': 'application/json; charset=UTF-8',
				'accept-encoding': 'gzip',
				'user-agent': 'okhttp/4.9.0',
			};

			const payload = {
				device_id: settings.deviceId!,
				product_id: settings.productId!,
				desired: JSON.stringify({
					state: {
						desired: processedCommand,
					},
				}),
			};

			const response = await fetch(`${this.apiBase}/device/command`, {
				method: 'POST',
				headers: headers,
				body: JSON.stringify(payload),
			});

			const data: any = await response.json();
			streamDeck.logger.info('Command response:', JSON.stringify(data));
			return data.message === 'SUCCESS';
		} catch (error) {
			streamDeck.logger.error('Command error:', error);
			return false;
		}
	}

	/**
	 * Draw temperature icon with current and target temperatures
	 */
	private async drawStatusInfoIcon(action: any, status: MSPAStatus, unit: string, timestamp?: number): Promise<void> {
		streamDeck.logger.info('Drawing temperature icon with status:', {
			water_temperature: status.water_temperature,
			temperature_setting: status.temperature_setting,
			heater_state: status.heater_state,
			unit: unit
		});

		// Ensure we have valid temperature data
		const waterTemp = status.water_temperature;
		if (waterTemp === undefined || waterTemp === null) {
			streamDeck.logger.error('Invalid water temperature data:', waterTemp);
			await this.drawErrorIcon(action);
			return;
		}

		const currentTemp = this.convertTemperature(waterTemp, unit);
		const targetTemp = status.temperature_setting ? this.convertTemperature(status.temperature_setting, unit) : null;
		const unitSymbol = unit.charAt(0).toUpperCase();

		streamDeck.logger.info('Converted temperatures:', {
			original: waterTemp,
			converted: currentTemp,
			target: targetTemp,
			unit: unitSymbol
		});

		// Add timestamp for unique image generation
		const now = timestamp || Date.now();
		const timeString = new Date(now).toLocaleTimeString('en-US', {
			hour12: true,
			hour: '2-digit',
			minute: '2-digit'
		});

		// Use simple SVG for icon generation since Canvas API is not available in Node.js context
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
          <rect width="144" height="144" fill="#1a1a1a" />

          <!-- Heater Icon -->
          <g transform="translate(16, 8)">
            <path 
              fill="none" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              d="M16,4
                C16,2.343 14.657,1 13,1
                C11.343,1 10,2.343 10,4
                V20
                C10,21.657 11.343,23 13,23
                C14.657,23 16,21.657 16,20
                V4
                Z" 
            />
            <circle 
              cx="13" 
              cy="22" 
              r="2" 
              fill="${status.heater_state ? '#00ff00' : '#ff4444'}" 
            />
            <rect 
              x="11" 
              y="16" 
              width="4" 
              height="5" 
              fill="${status.heater_state ? '#00ff00' : '#ff4444'}" 
            />
            <line 
              x1="11" 
              y1="8" 
              x2="15" 
              y2="8" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <line 
              x1="11" 
              y1="12" 
              x2="15" 
              y2="12" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <line 
              x1="11" 
              y1="16" 
              x2="15" 
              y2="16" 
              stroke="${status.heater_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
          </g>

          <!-- Filter Icon -->
          <g transform="translate(56, -1) scale(1, 1.3)">
            <path 
              d="M10,12 C10,10 12,8 16,8 C20,8 22,10 22,12 V20 C22,22 20,24 16,24 C12,24 10,22 10,20 Z" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
            />
            <path 
              d="M4,12 C5,11 6,13 7,12 C8,11 9,13 10,12" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M4,16 C5,15 6,17 7,16 C8,15 9,17 10,16" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M4,20 C5,19 6,21 7,20 C8,19 9,21 10,20" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M22,14 C23,13 24,15 25,14 C26,13 27,15 28,14" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
            <path 
              d="M22,18 C23,17 24,19 25,18 C26,17 27,19 28,18" 
              fill="none" 
              stroke="${status.filter_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
          </g>

          <!-- UVC Light Icon -->
          <g transform="translate(96, 1) scale(1, 1.2)">
            <circle 
              cx="16" 
              cy="20" 
              r="4" 
              fill="none" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
            />
            <line 
              x1="16" 
              y1="12" 
              x2="16" 
              y2="8" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="12" 
              y1="13" 
              x2="10" 
              y2="10" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="20" 
              y1="13" 
              x2="22" 
              y2="10" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="11" 
              y1="16" 
              x2="8" 
              y2="16" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <line 
              x1="21" 
              y1="16" 
              x2="24" 
              y2="16" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="2" 
              stroke-linecap="round" 
            />
            <path 
              d="M14,20 C14,19 15,18 16,18 C17,18 18,19 18,20" 
              fill="none" 
              stroke="${status.uvc_state ? '#00ff00' : '#ff4444'}" 
              stroke-width="1.5" 
            />
          </g>

          <text 
            x="72" 
            y="75" 
            text-anchor="middle" 
            fill="${status.heater_state ? '#00ff00' : '#888888'}" 
            font-family="Arial" 
            font-size="36" 
            font-weight="bold"
          >
            ${currentTemp}°${unitSymbol}
          </text>
          ${targetTemp
				? `<text 
                  x="72" 
                  y="100" 
                  text-anchor="middle" 
                  fill="#ffffff" 
                  font-family="Arial" 
                  font-size="16"
                >
                  Target: ${targetTemp}°${unitSymbol}
                </text>`
				: ''
			}
          <text 
            x="72" 
            y="135" 
            text-anchor="middle" 
            fill="#666666" 
            font-family="Arial" 
            font-size="21"
          >
            ${timeString}
          </text>
          <!-- Update: ${now} -->
		</svg>`;

		// Convert SVG to data URL
		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		streamDeck.logger.info(`Updating image: ${currentTemp}° ${unitSymbol} at ${timeString}`);
		await action.setImage(dataURL);
	}

	/**
	 * Draw configuration required icon
	 */
	private async drawConfigIcon(action: any): Promise<void> {
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
			<rect width="144" height="144" fill="#333333"/>
			<text x="72" y="65" text-anchor="middle" fill="#ffaa00" font-family="Arial" font-size="16" font-weight="bold">SETUP</text>
			<text x="72" y="90" text-anchor="middle" fill="#ffaa00" font-family="Arial" font-size="16" font-weight="bold">REQUIRED</text>
		</svg>`;

		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		await action.setImage(dataURL);
	}

	/**
	 * Draw error icon
	 */
	private async drawErrorIcon(action: any): Promise<void> {
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
			<rect width="144" height="144" fill="#330000"/>
			<text x="72" y="77" text-anchor="middle" fill="#ff4444" font-family="Arial" font-size="18" font-weight="bold">ERROR</text>
		</svg>`;

		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		await action.setImage(dataURL);
	}

	/**
	 * Draw loading icon with animated spinner
	 */
	private async drawLoadingIcon(action: any): Promise<void> {
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
			<rect width="144" height="144" fill="#2a2a2a"/>
			<text x="72" y="105" text-anchor="middle" fill="#cccccc" font-family="Arial" font-size="25">Loading...</text>
			<g transform="translate(72,50)">
				<circle cx="0" cy="0" r="20" fill="none" stroke="#00aaff" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
					<animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
					<animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
				</circle>
			</g>
		</svg>`;

		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		await action.setImage(dataURL);
	}

	/**
	 * Draw heater processing icon with animated spinner and specific action text
	 * @param action The action to update
	 * @param targetState The target heater state (1 for on, 0 for off)
	 */
	private async drawHeaterProcessingIcon(action: any, targetState: number): Promise<void> {
		const actionText = targetState === 1 ? 'On' : 'Off';
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
			<rect width="144" height="144" fill="#2a2a2a"/>
			<text x="72" y="95" text-anchor="middle" fill="#cccccc" font-family="Arial" font-size="18">Heater</text>
			<text x="72" y="115" text-anchor="middle" fill="#cccccc" font-family="Arial" font-size="18">${actionText}</text>
			<g transform="translate(72,40)">
				<circle cx="0" cy="0" r="18" fill="none" stroke="#ff8800" stroke-width="3" stroke-linecap="round" stroke-dasharray="28.274" stroke-dashoffset="28.274">
					<animate attributeName="stroke-dasharray" dur="2s" values="0 28.274;14.137 14.137;0 28.274" repeatCount="indefinite"/>
					<animate attributeName="stroke-dashoffset" dur="2s" values="0;-14.137;-28.274" repeatCount="indefinite"/>
				</circle>
			</g>
		</svg>`;

		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		await action.setImage(dataURL);
	}

	/**
	 * Draw all on/off processing icon with animated spinner and specific action text
	 * @param action The action to update
	 * @param targetState The target filter state (1 for on, 0 for off)
	 */
	private async drawAllOnOffProcessingIcon(action: any, targetState: number): Promise<void> {
		const actionText = targetState === 1 ? 'All On' : 'All Off';
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
			<rect width="144" height="144" fill="#2a2a2a"/>
			<text x="72" y="105" text-anchor="middle" fill="#cccccc" font-family="Arial" font-size="18">${actionText}</text>
			<g transform="translate(72,40)">
				<circle cx="0" cy="0" r="18" fill="none" stroke="#ff8800" stroke-width="3" stroke-linecap="round" stroke-dasharray="28.274" stroke-dashoffset="28.274">
					<animate attributeName="stroke-dasharray" dur="2s" values="0 28.274;14.137 14.137;0 28.274" repeatCount="indefinite"/>
					<animate attributeName="stroke-dashoffset" dur="2s" values="0;-14.137;-28.274" repeatCount="indefinite"/>
				</circle>
			</g>
		</svg>`;

		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		await action.setImage(dataURL);
	}

	/**
	 * Draw long press detected icon to show user they can release the button
	 * @param action The action to update
	 * @param targetState The target filter state (1 for on, 0 for off)
	 */
	private async drawLongPressDetectedIcon(action: any, targetState: number): Promise<void> {
		const actionText = targetState === 1 ? 'All On' : 'All Off';
		const svg = `<svg width="144" height="144" xmlns="http://www.w3.org/2000/svg">
			<rect width="144" height="144" fill="#2a2a2a"/>
			<text x="72" y="85" text-anchor="middle" fill="#00ff00" font-family="Arial" font-size="16" font-weight="bold">LONG PRESS</text>
			<text x="72" y="105" text-anchor="middle" fill="#00ff00" font-family="Arial" font-size="16" font-weight="bold">DETECTED</text>
			<text x="72" y="125" text-anchor="middle" fill="#cccccc" font-family="Arial" font-size="14">${actionText}</text>
			<g transform="translate(72,35)">
				<circle cx="0" cy="0" r="15" fill="none" stroke="#00ff00" stroke-width="2" stroke-linecap="round">
					<animate attributeName="r" dur="1s" values="10;15;10" repeatCount="indefinite"/>
					<animate attributeName="opacity" dur="1s" values="1;0.3;1" repeatCount="indefinite"/>
				</circle>
			</g>
		</svg>`;

		const dataURL = `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;

		await action.setImage(dataURL);
	}

	/**
	 * Convert temperature from API format to display format
	 */
	private convertTemperature(apiTemp: number, unit: string): number {
		const celsius = apiTemp / 2; // API returns doubled Celsius
		if (unit === 'fahrenheit') {
			return Math.round(((celsius * 9) / 5 + 32) * 10) / 10;
		}
		return Math.round(celsius * 10) / 10;
	}

	/**
	 * Validate that all required credentials are present
	 */
	private validateCredentials(settings: MSPASettings): boolean {
		const hasEmail = !!(settings.email);
		const hasPassword = !!(settings.passwordHash);
		const hasDeviceId = !!(settings.deviceId);
		const hasProductId = !!(settings.productId);

		streamDeck.logger.info('Credential validation:', {
			hasEmail,
			hasPassword,
			hasDeviceId,
			hasProductId,
			email: settings.email,
			deviceId: settings.deviceId,
			productId: settings.productId
		});

		return hasEmail && hasPassword && hasDeviceId && hasProductId;
	}

	/**
	 * Generate random nonce
	 */
	private generateNonce(length = 32): string {
		const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		let result = '';
		for (let i = 0; i < length; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length));
		}
		return result;
	}

	/**
	 * Get current timestamp
	 */
	private currentTimestamp(): string {
		return Math.floor(Date.now() / 1000).toString();
	}

	/**
	 * Build API signature
	 */
	private buildSignature(appId: string, nonce: string, ts: string, appSecret: string): string {
		const raw = `${appId},${appSecret},${nonce},${ts}`;
		return createHash('md5').update(raw, 'utf8').digest('hex').toUpperCase();
	}

	/**
	 * Convert boolean values to numbers (1/0) for MSPA API compatibility
	 */
	private convertBooleansToNumbers(obj: any): any {
		if (obj === null || obj === undefined) {
			return obj;
		}

		if (typeof obj === 'boolean') {
			return obj ? 1 : 0;
		}

		if (Array.isArray(obj)) {
			return obj.map(item => this.convertBooleansToNumbers(item));
		}

		if (typeof obj === 'object') {
			const converted: any = {};
			for (const [key, value] of Object.entries(obj)) {
				converted[key] = this.convertBooleansToNumbers(value);
			}
			return converted;
		}

		return obj;
	}
}

/**
 * MSPA Device Status interface based on API response
 */
interface MSPAStatus {
	wifivertion: number;
	otastatus: number;
	mcuversion: string;
	ConnectType: string;
	heater_state: number;
	filter_state: number;
	bubble_state: number;
	ozone_state: number;
	uvc_state: number;
	jet_state: number;
	temperature_unit: number;
	temperature_setting: number;
	water_temperature: number;
	auto_inflate: number;
	filter_current: number;
	safety_lock: number;
	heat_time_switch: number;
	heat_state: number;
	multimcuotainfo: string;
	bubble_level: number;
	filter_life: number;
	heat_time: number;
	trdversion: string;
	is_online: boolean;
	fault: string;
	warning: string;
	device_heat_perhour: number;
}

/**
 * Settings for MSPA Control action
 */
type MSPASettings = {
	email?: string;
	passwordHash?: string;
	deviceId?: string;
	productId?: string;
	temperatureUnit?: 'celsius' | 'fahrenheit';
	refreshInterval?: number; // in seconds
	savedToken?: string; // Cached authentication token
};